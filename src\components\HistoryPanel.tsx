import { FC, useState, useMemo } from "react";
import { PromptHistory, EnhancementMode, EnhancementStyle } from "../types";

interface HistoryPanelProps {
  history: PromptHistory[];
  onSelectHistory: (item: PromptHistory) => void;
  onDeleteHistory: (id: string) => void;
  onClearHistory: () => void;
  className?: string;
}

export const HistoryPanel: FC<HistoryPanelProps> = ({
  history,
  onSelectHistory,
  onDeleteHistory,
  onClearHistory,
  className = "",
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterMode, setFilterMode] = useState<EnhancementMode | "all">("all");
  const [sortBy, setSortBy] = useState<"timestamp" | "rating">("timestamp");

  const filteredAndSortedHistory = useMemo(() => {
    let filtered = history;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(
        (item) =>
          item.originalPrompt
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
          item.enhancedPrompt
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
          item.tags?.some((tag) =>
            tag.toLowerCase().includes(searchTerm.toLowerCase())
          ) ||
          item.category?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by mode
    if (filterMode !== "all") {
      filtered = filtered.filter((item) => item.mode === filterMode);
    }

    // Sort
    filtered.sort((a, b) => {
      if (sortBy === "timestamp") {
        return (
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        );
      } else {
        return (b.rating || 0) - (a.rating || 0);
      }
    });

    return filtered;
  }, [history, searchTerm, filterMode, sortBy]);

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getModeColor = (mode: EnhancementMode) => {
    switch (mode) {
      case "quick":
        return "bg-blue-100 text-blue-800";
      case "structured":
        return "bg-green-100 text-green-800";
      case "template":
        return "bg-purple-100 text-purple-800";
      case "batch":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const renderStars = (rating?: number) => {
    if (!rating) return null;

    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <span
            key={star}
            className={`text-xs ${
              star <= rating ? "text-yellow-400" : "text-gray-300"
            }`}
          >
            ★
          </span>
        ))}
      </div>
    );
  };

  return (
    <div
      className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}
    >
      <div className="px-4 py-3 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-gray-900">History</h3>
          <button
            onClick={onClearHistory}
            className="text-xs text-red-600 hover:text-red-800"
            disabled={history.length === 0}
          >
            Clear All
          </button>
        </div>

        {/* Search */}
        <input
          type="text"
          placeholder="Search history..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full px-3 py-2 text-sm bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        />

        {/* Filters */}
        <div className="flex items-center space-x-2 mt-2">
          <select
            value={filterMode}
            onChange={(e) =>
              setFilterMode(e.target.value as EnhancementMode | "all")
            }
            className="text-xs px-2 py-1 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="all">All Modes</option>
            <option value="quick">Quick</option>
            <option value="structured">Structured</option>
            <option value="template">Template</option>
            <option value="batch">Batch</option>
          </select>

          <select
            value={sortBy}
            onChange={(e) =>
              setSortBy(e.target.value as "timestamp" | "rating")
            }
            className="text-xs px-2 py-1 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="timestamp">Recent</option>
            <option value="rating">Rating</option>
          </select>
        </div>
      </div>

      <div className="max-h-96 overflow-y-auto">
        {filteredAndSortedHistory.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <p className="text-sm">
              {history.length === 0 ? "No history yet" : "No matching results"}
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredAndSortedHistory.map((item) => (
              <div
                key={item.id}
                className="p-3 hover:bg-gray-50 cursor-pointer group"
                onClick={() => onSelectHistory(item)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <span
                        className={`px-2 py-1 text-xs font-medium rounded ${getModeColor(
                          item.mode
                        )}`}
                      >
                        {item.mode}
                      </span>
                      {item.category && (
                        <span className="text-xs text-gray-500">
                          {item.category}
                        </span>
                      )}
                      {renderStars(item.rating)}
                    </div>

                    <p className="text-sm text-gray-900 line-clamp-2 mb-1">
                      {item.originalPrompt}
                    </p>

                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">
                        {formatDate(item.timestamp)}
                      </span>

                      {item.tags && item.tags.length > 0 && (
                        <div className="flex items-center space-x-1">
                          {item.tags.slice(0, 2).map((tag) => (
                            <span
                              key={tag}
                              className="px-1 py-0.5 text-xs bg-gray-100 text-gray-600 rounded"
                            >
                              {tag}
                            </span>
                          ))}
                          {item.tags.length > 2 && (
                            <span className="text-xs text-gray-400">
                              +{item.tags.length - 2}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onDeleteHistory(item.id);
                    }}
                    className="ml-2 opacity-0 group-hover:opacity-100 text-red-500 hover:text-red-700 transition-opacity"
                  >
                    <svg
                      className="w-4 h-4"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
