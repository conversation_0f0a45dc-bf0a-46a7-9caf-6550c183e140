# Prompt Enhancer Desktop App

A desktop application built with Electron and React that enhances prompts using Google's Gemini AI API.

## Phase 1: Core Functionality ✅

This implementation includes:

- ✅ Basic Electron app setup
- ✅ Editor implementation with Monaco Editor
- ✅ Gemini API integration
- ✅ Simple enhancement mode with multiple styles

## Features

### Enhancement Modes

- **Quick Enhancement**: Fast improvements for clarity and effectiveness
- **Structured Mode**: Organized prompts with clear sections
- **Template Mode**: Reusable template structures

### Enhancement Styles

- **Detailed**: Comprehensive, detailed instructions
- **Concise**: Brief and to-the-point
- **Creative**: Emphasizes creativity and exploration
- **Technical**: Technical accuracy and precision

### Core Features

- Monaco Editor for advanced text editing
- Real-time character and word count
- Copy to clipboard functionality
- API key management with secure storage
- Error handling and loading states

## Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Google Gemini API key

## Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd prompt-enhancer
```

2. Install dependencies:

```bash
npm install
```

3. Get your Gemini API key:
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Sign in with your Google account
   - Create an API key
   - Copy the generated key

## Development

Run the development server:

```bash
npm run dev
```

This will start both the React development server and the Electron app.

## Building

Build the application:

```bash
npm run build
```

Package for distribution:

```bash
# For current platform
npm run package

# For specific platforms
npm run package:win
npm run package:mac
npm run package:linux
```

## Usage

1. Launch the application
2. Enter your Gemini API key when prompted
3. Select an enhancement mode and style
4. Enter your prompt in the input editor
5. Click "Enhance Prompt" to get an improved version
6. Copy the result or use it as input for further enhancement

## Project Structure

```
prompt-enhancer/
├── electron/           # Electron main process files
│   ├── main.ts        # Main Electron process
│   ├── preload.ts     # Preload script for IPC
│   └── utils.ts       # Utility functions
├── src/               # React application
│   ├── components/    # React components
│   ├── services/      # API services
│   ├── types/         # TypeScript definitions
│   └── App.tsx        # Main app component
├── dist/              # Built files
└── release/           # Packaged applications
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details
